"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  Calendar,
  Flag,
  Link as LinkIcon,
  MoreHorizontal,
  Share2,
  UserPlus,
  Users,
  Verified,
  VolumeX,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { CryptoWhaleData } from "../hooks/useCryptoWhaleData";

interface TwitterProfileContentProps {
  profileData: CryptoWhaleData;
  onFollowersClick: () => void;
  onFollowingClick: () => void;
  bannerImage?: string;
}

export function TwitterProfileContent({
  profileData,
  onFollowersClick,
  onFollowingClick,
  bannerImage = "https://images.unsplash.com/photo-1639762681057-408e52192e55?q=80&w=2832&auto=format&fit=crop",
}: TwitterProfileContentProps) {
  const [isFollowing, setIsFollowing] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  return (
    <>
      <div className="sticky top-0 z-10 backdrop-blur-md bg-black/70 p-4 border-b border-gray-800">
        <div className="flex items-center gap-6">
          <Link href="#" className="rounded-full hover:bg-gray-800 p-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </Link>
          <div>
            <h1 className="font-bold text-xl">{profileData.name}</h1>
            <p className="text-gray-500 text-sm">1,587 posts</p>
          </div>
        </div>
      </div>

      <div className="relative">
        <div className="h-48 bg-gray-800">
          <Image
            src={bannerImage}
            alt="Profile Banner"
            width={600}
            height={200}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="absolute -bottom-16 left-4">
          <Avatar className="w-32 h-32 border-4 border-black">
            <AvatarImage src={profileData.avatar} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
        </div>
        <div className="flex justify-end p-4 gap-2">
          <div className="relative">
            <Button
              variant="outline"
              size="icon"
              className="rounded-full border-gray-600 text-white hover:bg-gray-800 cursor-pointer"
              onClick={() => setShowDropdown(!showDropdown)}
            >
              <MoreHorizontal className="h-5 w-5" />
            </Button>

            {showDropdown && (
              <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-black border border-gray-700 z-50">
                <div className="py-1">
                  {[
                    { icon: Flag, label: "Report profile" },
                    {
                      icon: VolumeX,
                      label: `Block @${profileData.username.substring(1)}`,
                    },
                    { icon: UserPlus, label: "Add/remove from Lists" },
                    { icon: Share2, label: "Share profile via..." },
                    { icon: LinkIcon, label: "Copy link to profile" },
                  ].map((item) => {
                    const IconComponent = item.icon;
                    return (
                      <button
                        key={item.label}
                        className="flex items-center w-full px-4 py-2 text-sm text-white hover:bg-gray-800 cursor-pointer"
                      >
                        <IconComponent className="mr-2 h-4 w-4" />
                        {item.label}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          <Button
            variant={isFollowing ? "outline" : "default"}
            className={`rounded-full font-bold cursor-pointer ${
              isFollowing
                ? "border-gray-600 text-white hover:border-red-500 hover:text-red-500 hover:bg-red-500/10"
                : "bg-white text-black hover:bg-gray-200"
            }`}
            onClick={() => setIsFollowing(!isFollowing)}
          >
            {isFollowing ? "Following" : "Follow"}
          </Button>
        </div>
      </div>

      <div className="pt-16 px-4 pb-4">
        <div className="flex items-center gap-1">
          <h2 className="font-bold text-xl">{profileData.name}</h2>
          {profileData.isVerified && (
            <Verified className="w-5 h-5 text-[#1d9bf0]" />
          )}
        </div>
        <p className="text-gray-500">{profileData.username}</p>
        <p className="mt-3">{profileData.description}</p>
        <div className="flex flex-wrap gap-4 mt-3 text-gray-500">
          <div className="flex items-center gap-1">
            <Users className="w-4 h-4" />
            <span>Crypto Investor</span>
          </div>
          <div className="flex items-center gap-1">
            <Link href="#" className="hover:underline">
              <span>cryptowhale.finance</span>
            </Link>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            <span>Joined {profileData.joinDate}</span>
          </div>
        </div>
        <div className="flex gap-5 mt-3">
          <button
            onClick={onFollowingClick}
            className="hover:underline text-left cursor-pointer"
          >
            <span className="font-bold">{profileData.following}</span>{" "}
            <span className="text-gray-500">Following</span>
          </button>
          <button
            onClick={onFollowersClick}
            className="hover:underline text-left cursor-pointer"
          >
            <span className="font-bold">{profileData.followers}</span>{" "}
            <span className="text-gray-500">Followers</span>
          </button>
        </div>
      </div>

      <div className="w-full">
        <div className="w-full grid grid-cols-4 bg-transparent border-b border-gray-800">
          <div className="border-b-4 border-[#1d9bf0] rounded-none py-4 text-white font-medium text-center">
            Posts
          </div>
          <div className="rounded-none py-4 text-gray-500 text-center">
            Replies
          </div>
          <div className="rounded-none py-4 text-gray-500 text-center">
            Media
          </div>
          <div className="rounded-none py-4 text-gray-500 text-center">
            Likes
          </div>
        </div>

        <TwitterProfilePosts profileData={profileData} />
      </div>
    </>
  );
}

interface TwitterProfilePostsProps {
  profileData: CryptoWhaleData;
}

export function TwitterProfilePosts({ profileData }: TwitterProfilePostsProps) {
  return (
    <div className="mt-0">
      <div className="border-b border-gray-800 p-4 hover:bg-gray-900/50 cursor-pointer transition">
        <div className="flex gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={profileData.avatar} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-1">
              <span className="font-bold">{profileData.name}</span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-[#1d9bf0]" />
              )}
              <span className="text-gray-500">{profileData.username} · 2h</span>
            </div>
            <p className="mt-1">
              🚀 Excited to announce our latest investment in @web3game_xyz -
              the future of gaming on the blockchain! Join us on our podcast
              tomorrow as we talk with the founders about their vision.
            </p>
            <div className="mt-3 rounded-2xl overflow-hidden border border-gray-800">
              <Image
                src="https://images.unsplash.com/photo-1620321023374-d1a68fbc720d?q=80&w=2574&auto=format&fit=crop"
                alt="Web3 Game"
                width={500}
                height={300}
                className="w-full h-auto"
              />
            </div>
            <TwitterPostActions comments={42} reposts={128} likes={512} />
          </div>
        </div>
      </div>

      <div className="border-b border-gray-800 p-4 hover:bg-gray-900/50 cursor-pointer transition">
        <div className="flex gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={profileData.avatar} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-1">
              <span className="font-bold">{profileData.name}</span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-[#1d9bf0]" />
              )}
              <span className="text-gray-500">{profileData.username} · 6h</span>
            </div>
            <p className="mt-1">
              📊 Just published our analysis on the recent ETH price movement.
              The merge is coming, and we're seeing significant institutional
              interest. Our prediction: $3500 by EOY. What's your take?
            </p>
            <div className="mt-3 rounded-2xl overflow-hidden border border-gray-800">
              <Image
                src="https://images.unsplash.com/photo-1621761191319-c6fb62004040?q=80&w=2574&auto=format&fit=crop"
                alt="Ethereum Chart"
                width={500}
                height={300}
                className="w-full h-auto"
              />
            </div>
            <TwitterPostActions comments={89} reposts={176} likes={823} />
          </div>
        </div>
      </div>

      <div className="border-b border-gray-800 p-4 hover:bg-gray-900/50 cursor-pointer transition">
        <div className="flex gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={profileData.avatar} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-1">
              <span className="font-bold">{profileData.name}</span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-[#1d9bf0]" />
              )}
              <span className="text-gray-500">
                {profileData.username} · 12h
              </span>
            </div>
            <p className="mt-1">
              🔐 Security reminder: Never share your seed phrase, never click
              suspicious links, and always verify smart contracts before
              interacting. The recent phishing attacks targeting DeFi users are
              getting more sophisticated.
            </p>
            <TwitterPostActions comments={124} reposts={342} likes={1567} />
          </div>
        </div>
      </div>

      <div className="border-b border-gray-800 p-4 hover:bg-gray-900/50 cursor-pointer transition">
        <div className="flex gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={profileData.avatar} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-1">
              <span className="font-bold">{profileData.name}</span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-[#1d9bf0]" />
              )}
              <span className="text-gray-500">
                {profileData.username} · 18h
              </span>
            </div>
            <p className="mt-1">
              💎 Just picked up another Bored Ape! The NFT market is showing
              strong signs of recovery. Excited to be part of the @BoredApeYC
              community and exploring new metaverse opportunities with this IP.
            </p>
            <div className="mt-3 rounded-2xl overflow-hidden border border-gray-800">
              <Image
                src="https://images.unsplash.com/photo-1646483236848-3f8a2aa8bd1f?q=80&w=2574&auto=format&fit=crop"
                alt="NFT Artwork"
                width={500}
                height={300}
                className="w-full h-auto"
              />
            </div>
            <TwitterPostActions comments={56} reposts={98} likes={734} />
          </div>
        </div>
      </div>

      <div className="border-b border-gray-800 p-4 hover:bg-gray-900/50 cursor-pointer transition">
        <div className="flex gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={profileData.avatar} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-1">
              <span className="font-bold">{profileData.name}</span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-[#1d9bf0]" />
              )}
              <span className="text-gray-500">
                {profileData.username} · 22h
              </span>
            </div>
            <p className="mt-1">
              🌉 Cross-chain bridges are the future of DeFi. Just moved assets
              from Ethereum to Solana in minutes with minimal fees.
              Interoperability is no longer just a buzzword - it's happening
              now. Bullish on projects building in this space.
            </p>
            <TwitterPostActions comments={67} reposts={112} likes={458} />
          </div>
        </div>
      </div>

      <div className="border-b border-gray-800 p-4 hover:bg-gray-900/50 cursor-pointer transition">
        <div className="flex gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={profileData.avatar} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-1">
              <span className="font-bold">{profileData.name}</span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-[#1d9bf0]" />
              )}
              <span className="text-gray-500">{profileData.username} · 1d</span>
            </div>
            <p className="mt-1">
              🏛️ Regulatory clarity is coming to crypto. Just attended a
              closed-door session with policymakers. The future looks promising
              for compliant projects. This is why we've always emphasized KYC
              and AML in our investments.
            </p>
            <div className="mt-3 rounded-2xl overflow-hidden border border-gray-800">
              <Image
                src="https://images.unsplash.com/photo-1607026151739-e7ce1b4c2b19?q=80&w=2574&auto=format&fit=crop"
                alt="Regulatory Meeting"
                width={500}
                height={300}
                className="w-full h-auto"
              />
            </div>
            <TwitterPostActions comments={103} reposts={187} likes={921} />
          </div>
        </div>
      </div>

      <div className="border-b border-gray-800 p-4 hover:bg-gray-900/50 cursor-pointer transition">
        <div className="flex gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={profileData.avatar} alt={profileData.name} />
            <AvatarFallback>{profileData.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-1">
              <span className="font-bold">{profileData.name}</span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-[#1d9bf0]" />
              )}
              <span className="text-gray-500">{profileData.username} · 2d</span>
            </div>
            <p className="mt-1">
              💰 Looking for seed funding for your web3 project? We're accepting
              applications for our Q3 investment round. DM us with your pitch
              deck or visit opsek.io/funding
            </p>
            <TwitterPostActions comments={76} reposts={245} likes={1200} />
          </div>
        </div>
      </div>
    </div>
  );
}

interface TwitterPostActionsProps {
  comments: number;
  reposts: number;
  likes: number;
}

export function TwitterPostActions({
  comments,
  reposts,
  likes,
}: TwitterPostActionsProps) {
  return (
    <div className="flex justify-between mt-3 text-gray-500">
      <button className="flex items-center gap-1 hover:text-[#1d9bf0] cursor-pointer">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
        </svg>
        <span>{comments}</span>
      </button>
      <button className="flex items-center gap-1 hover:text-green-500 cursor-pointer">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M7 13l5 5 5-5M7 6l5 5 5-5" />
        </svg>
        <span>{reposts}</span>
      </button>
      <button className="flex items-center gap-1 hover:text-pink-500 cursor-pointer">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
        </svg>
        <span>{likes >= 1000 ? `${(likes / 1000).toFixed(1)}K` : likes}</span>
      </button>
      <button className="flex items-center gap-1 hover:text-[#1d9bf0] cursor-pointer">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
          <polyline points="16 6 12 2 8 6" />
          <line x1="12" y1="2" x2="12" y2="15" />
        </svg>
      </button>
    </div>
  );
}
