"use client";

import { useState } from "react";
import { Users } from "lucide-react";
import { FollowerItem } from "./twitter-follower-item";
import { TwitterFollower } from "../utils/twitter-utils";

export type FollowersTab = "verified" | "know" | "followers" | "following";

interface TwitterFollowersTabsProps {
  initialTab?: FollowersTab;
  verifiedFollowers: TwitterFollower[];
  followers: TwitterFollower[];
  following: TwitterFollower[];
}

export function TwitterFollowersTabs({
  initialTab = "verified",
  verifiedFollowers,
  followers,
  following,
}: TwitterFollowersTabsProps) {
  const [activeTab, setActiveTab] = useState<FollowersTab>(initialTab);

  const renderTabContent = () => {
    switch (activeTab) {
      case "verified":
        return (
          <div className="mt-0">
            {verifiedFollowers.map((follower) => (
              <FollowerItem key={follower.id} follower={follower} />
            ))}
          </div>
        );
      case "know":
        return (
          <div className="flex flex-col items-center justify-center py-16 text-gray-500">
            <Users className="w-12 h-12 mb-4" />
            <h3 className="text-xl font-bold mb-2">No followers you know</h3>
            <p className="text-center max-w-xs">
              When people you follow also follow this account, they'll show up
              here.
            </p>
          </div>
        );
      case "followers":
        return (
          <div className="mt-0">
            {followers.map((follower) => (
              <FollowerItem key={follower.id} follower={follower} />
            ))}
          </div>
        );
      case "following":
        return (
          <div className="mt-0">
            {following.map((follower) => (
              <FollowerItem key={follower.id} follower={follower} />
            ))}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full">
      <div className="w-full grid grid-cols-4 bg-transparent border-b border-gray-800">
        {[
          { key: "verified", label: "Verified" },
          { key: "know", label: "You Know" },
          { key: "followers", label: "Followers" },
          { key: "following", label: "Following" },
        ].map((tab) => (
          <div
            key={tab.key}
            className={`py-4 text-center cursor-pointer ${
              activeTab === tab.key
                ? "border-b-4 border-[#1d9bf0] text-white font-medium"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab(tab.key as FollowersTab)}
          >
            {tab.label}
          </div>
        ))}
      </div>

      {renderTabContent()}
    </div>
  );
}
