"use client";

import { ReactNode } from "react";
import { ChromeBrowserMock } from "./chrome-browser";
import { ChromeProvider } from "./chrome-context";
import { WindowIcon } from "./common/window-icon";
import { DraggableWindow } from "./draggable-window";
import { FloatingDock } from "./floating-dock";
import { MacOSProvider, WindowConfig, useMacOS } from "./macos-context";
import { MacOSTopBar } from "./macos-topbar";
import { NoteFrame } from "./notes-frame";
import { CreatorBanner } from "@/components/creator-banner";

interface MacOSProps {
  chromeConfig?: {
    tabs: {
      name: string;
      url: string;
      content: ReactNode;
      closable?: boolean;
    }[];
    extensions?: {
      id: string;
      name: string;
      icon: string;
      content: ReactNode;
      active: boolean;
    }[];
  };
  gameInstructions?: ReactNode;
  zoom?: {
    content?: ReactNode;
  };
  chatgpt?: {
    content?: ReactNode;
  };
}

export function MacOS({
  chromeConfig,
  gameInstructions,
  zoom,
  chatgpt,
}: MacOSProps) {
  console.log("MacOS - Chrome Config:", chromeConfig);

  const baseWindowsData = [
    {
      id: "notes",
      type: "notes" as const,
      title: "Notes",
      iconSrc: "/dock-icons/icon-notes.webp",
      content: (
        <NoteFrame title="Game Instructions" instructions={gameInstructions} />
      ),
      position: { x: 100, y: 100 },
      size: { width: 900, height: 700 },
      isOpen: true,
    },
    {
      id: "chrome",
      type: "chrome" as const,
      title: "Chrome",
      iconSrc: "/dock-icons/chrome.png",
      content: (
        <ChromeProvider
          initialTabs={chromeConfig?.tabs || []}
          initialExtensions={chromeConfig?.extensions || []}
        >
          <ChromeBrowserMock url={chromeConfig?.tabs[0]?.url}>
            <div className="p-4">Default browser content</div>
          </ChromeBrowserMock>
        </ChromeProvider>
      ),
      position: { x: 200, y: 100 },
      size: { width: 1300, height: 700 },
      isOpen: false,
    },
    {
      id: "chatgpt",
      type: "ai" as const,
      title: "ChatGPT",
      iconSrc: "/dock-icons/chatgpt.webp",
      content: chatgpt?.content || <div className="p-4">ChatGPT interface</div>,
      position: { x: 300, y: 80 },
      size: { width: 700, height: 700 },
      isOpen: false,
    },
  ];

  const initialWindows: WindowConfig[] = baseWindowsData.map((window) => ({
    ...window,
    icon: <WindowIcon src={window.iconSrc} alt={window.title} />,
  }));

  if (zoom) {
    initialWindows.push({
      id: "zoom",
      type: "zoom",
      title: "Zoom",
      icon: <WindowIcon src="/dock-icons/zoom.webp" alt="Zoom" />,
      content: zoom.content || <div className="p-4">Zoom meeting</div>,
      position: { x: 200, y: 100 },
      size: { width: 1200, height: 700 },
      isOpen: false,
    });
  }

  return (
    <MacOSProvider initialWindows={initialWindows}>
      <MacOSScreen />
    </MacOSProvider>
  );
}

function MacOSScreen() {
  return (
    <div className="relative h-screen w-full overflow-hidden bg-gradient-to-b from-blue-400 to-purple-500">
      <MacOSTopBar />
      <div className="h-full w-full pt-6">
        <WindowManager />
      </div>
      <FloatingDock />
      <div className="absolute bottom-0 left-0 right-0 z-50">
        <CreatorBanner
          variant="light"
          className="bg-black/20 backdrop-blur-sm"
        />
      </div>
    </div>
  );
}

function WindowManager() {
  const { windows, isWindowOpen } = useMacOS();

  return (
    <>
      {windows.map((window) => {
        if (!isWindowOpen(window.id)) return null;

        let minWidth = 400;
        let minHeight = 300;

        if (window.type === "chrome") {
          minWidth = 800;
          minHeight = 500;
        }

        return (
          <DraggableWindow
            key={window.id}
            id={window.id}
            title={window.title}
            initialPosition={window.position}
            initialSize={window.size}
            minWidth={minWidth}
            minHeight={minHeight}
          >
            {window.content}
          </DraggableWindow>
        );
      })}
    </>
  );
}
